import { useState } from 'react'

function AddTaskForm({ categoryName, onAddTask, onCancel }) {
  const [taskText, setTaskText] = useState('')
  const [error, setError] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()

    // Validation
    if (!taskText.trim()) {
      setError('Task description is required')
      return
    }

    if (taskText.trim().length < 3) {
      setError('Task description must be at least 3 characters long')
      return
    }

    if (taskText.trim().length > 200) {
      setError('Task description must be less than 200 characters')
      return
    }

    const success = onAddTask(categoryName, taskText.trim())
    if (success) {
      setTaskText('')
      setError('')
    } else {
      setError('Failed to add task')
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Escape') {
      onCancel()
    }
  }

  // Format category name for display
  const formatCategoryName = (name) => {
    return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  // Get category icon based on name
  const getCategoryIcon = (name) => {
    const icons = {
      personal: '👤',
      work: '💼',
      shopping: '🛒',
      health: '🏥',
      fitness: '💪',
      education: '📚',
      travel: '✈️',
      home: '🏠',
      finance: '💰',
      hobby: '🎨'
    }
    return icons[name.toLowerCase()] || '📋'
  }

  // Task suggestions based on category
  const getTaskSuggestions = (category) => {
    const suggestions = {
      personal: [
        'Call family members',
        'Read a book',
        'Practice meditation',
        'Write in journal',
        'Plan weekend activities'
      ],
      work: [
        'Review project requirements',
        'Attend team meeting',
        'Update project documentation',
        'Send status report',
        'Prepare presentation'
      ],
      shopping: [
        'Buy groceries',
        'Get household supplies',
        'Purchase birthday gift',
        'Buy new clothes',
        'Get office supplies'
      ],
      health: [
        'Schedule doctor appointment',
        'Take vitamins',
        'Drink 8 glasses of water',
        'Get annual checkup',
        'Update medical records'
      ],
      fitness: [
        'Go for a run',
        'Do yoga session',
        'Hit the gym',
        'Take a walk',
        'Do strength training'
      ]
    }
    return suggestions[category.toLowerCase()] || [
      'Complete important task',
      'Review and organize',
      'Plan next steps',
      'Follow up on progress',
      'Set new goals'
    ]
  }

  const suggestions = getTaskSuggestions(categoryName)

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fade-in">
      <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 animate-bounce-in">
        <div className="text-center mb-6">
          <div className="text-4xl mb-2">{getCategoryIcon(categoryName)}</div>
          <h2 className="text-xl font-semibold text-gray-800">
            Add New Task
          </h2>
          <p className="text-gray-600 text-sm">
            Add a task to "{formatCategoryName(categoryName)}" category
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="taskText" className="block text-sm font-medium text-gray-700 mb-2">
              Task Description
            </label>
            <textarea
              id="taskText"
              value={taskText}
              onChange={(e) => {
                setTaskText(e.target.value)
                setError('')
              }}
              onKeyDown={handleKeyPress}
              className="input-field resize-none"
              placeholder="Enter task description..."
              rows="3"
              autoFocus
            />
            <div className="flex justify-between items-center mt-1">
              {error ? (
                <p className="text-red-500 text-xs">{error}</p>
              ) : (
                <p className="text-gray-400 text-xs">
                  {taskText.length}/200 characters
                </p>
              )}
            </div>
          </div>

          {/* Quick Suggestions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quick Suggestions
            </label>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => setTaskText(suggestion)}
                  className="w-full text-left p-2 text-sm bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!taskText.trim()}
            >
              Add Task
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddTaskForm
