function ProgressBar({ completed, total, percentage }) {
  const getProgressColor = () => {
    if (percentage === 100) return 'bg-success-500'
    if (percentage >= 75) return 'bg-blue-500'
    if (percentage >= 50) return 'bg-yellow-500'
    if (percentage >= 25) return 'bg-orange-500'
    return 'bg-red-500'
  }

  const getProgressText = () => {
    if (total === 0) return 'No tasks'
    if (percentage === 100) return '🎉 All done!'
    return `${completed}/${total} completed`
  }

  return (
    <div className="space-y-2">
      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
        <div
          className={`h-full transition-all duration-500 ease-out ${getProgressColor()}`}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {/* Progress Text */}
      <div className="flex justify-between items-center text-xs">
        <span className="font-medium">
          {getProgressText()}
        </span>
        <span className="opacity-75">
          {Math.round(percentage)}%
        </span>
      </div>
    </div>
  )
}

export default ProgressBar
