import { useState } from 'react'

function AddCategoryForm({ onAddCategory, onCancel }) {
  const [categoryName, setCategoryName] = useState('')
  const [error, setError] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()

    // Validation
    if (!categoryName.trim()) {
      setError('Category name is required')
      return
    }

    if (categoryName.trim().length < 2) {
      setError('Category name must be at least 2 characters long')
      return
    }

    if (categoryName.trim().length > 30) {
      setError('Category name must be less than 30 characters')
      return
    }

    // Check for valid characters (letters, numbers, spaces, hyphens, underscores)
    if (!/^[a-zA-Z0-9\s\-_]+$/.test(categoryName.trim())) {
      setError('Category name can only contain letters, numbers, spaces, hyphens, and underscores')
      return
    }

    const success = onAddCategory(categoryName.trim())
    if (success) {
      setCategoryName('')
      setError('')
    } else {
      setError('Category already exists')
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Escape') {
      onCancel()
    }
  }

  // Predefined category suggestions
  const suggestions = [
    { name: 'Personal', icon: '👤' },
    { name: 'Work', icon: '💼' },
    { name: 'Shopping', icon: '🛒' },
    { name: 'Health', icon: '🏥' },
    { name: 'Fitness', icon: '💪' },
    { name: 'Education', icon: '📚' },
    { name: 'Travel', icon: '✈️' },
    { name: 'Home', icon: '🏠' },
    { name: 'Finance', icon: '💰' },
    { name: 'Hobby', icon: '🎨' }
  ]

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fade-in">
      <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 animate-bounce-in">
        <div className="text-center mb-6">
          <div className="text-4xl mb-2">📁</div>
          <h2 className="text-xl font-semibold text-gray-800">
            Add New Category
          </h2>
          <p className="text-gray-600 text-sm">
            Create a new category to organize your tasks
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700 mb-2">
              Category Name
            </label>
            <input
              type="text"
              id="categoryName"
              value={categoryName}
              onChange={(e) => {
                setCategoryName(e.target.value)
                setError('')
              }}
              onKeyDown={handleKeyPress}
              className="input-field"
              placeholder="Enter category name..."
              autoFocus
            />
            {error && (
              <p className="text-red-500 text-xs mt-1">{error}</p>
            )}
          </div>

          {/* Quick Suggestions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quick Suggestions
            </label>
            <div className="grid grid-cols-2 gap-2">
              {suggestions.map((suggestion) => (
                <button
                  key={suggestion.name}
                  type="button"
                  onClick={() => setCategoryName(suggestion.name)}
                  className="flex items-center gap-2 p-2 text-left text-sm bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <span>{suggestion.icon}</span>
                  <span>{suggestion.name}</span>
                </button>
              ))}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!categoryName.trim()}
            >
              Create Category
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddCategoryForm
