{"version": 3, "file": "create.js", "sourceRoot": "", "sources": ["../../src/create.ts"], "names": [], "mappings": ";;;;;;AAAA,qDAAkE;AAElE,0DAA4B;AAC5B,uCAAgC;AAChC,uDAA+C;AAO/C,uCAA0C;AAE1C,MAAM,cAAc,GAAG,CAAC,GAAuB,EAAE,KAAe,EAAE,EAAE;IAClE,MAAM,CAAC,GAAG,IAAI,kBAAQ,CAAC,GAAG,CAAC,CAAA;IAC3B,MAAM,MAAM,GAAG,IAAI,6BAAe,CAAC,GAAG,CAAC,IAAI,EAAE;QAC3C,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK;KACxB,CAAC,CAAA;IACF,CAAC,CAAC,IAAI,CAAC,MAAsC,CAAC,CAAA;IAC9C,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AACxB,CAAC,CAAA;AAED,MAAM,UAAU,GAAG,CAAC,GAAmB,EAAE,KAAe,EAAE,EAAE;IAC1D,MAAM,CAAC,GAAG,IAAI,cAAI,CAAC,GAAG,CAAC,CAAA;IACvB,MAAM,MAAM,GAAG,IAAI,yBAAW,CAAC,GAAG,CAAC,IAAI,EAAE;QACvC,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK;KACxB,CAAC,CAAA;IACF,CAAC,CAAC,IAAI,CAAC,MAAsC,CAAC,CAAA;IAE9C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC7C,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QACvB,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QACvB,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;IACpB,CAAC,CAAC,CAAA;IAEF,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IAEvB,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,CAAC,CAAW,EAAE,KAAe,EAAE,EAAE;IACpD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC3B,IAAA,cAAI,EAAC;gBACH,IAAI,EAAE,mBAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;aACnC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAC,CAAA;IACF,CAAC,CAAC,GAAG,EAAE,CAAA;AACT,CAAC,CAAA;AAED,MAAM,aAAa,GAAG,KAAK,EACzB,CAAO,EACP,KAAe,EACA,EAAE;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC3B,MAAM,IAAA,cAAI,EAAC;gBACT,IAAI,EAAE,mBAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChD,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,KAAK,CAAC,EAAE;oBACnB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;gBACd,CAAC;aACF,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACb,CAAC;IACH,CAAC;IACD,CAAC,CAAC,GAAG,EAAE,CAAA;AACT,CAAC,CAAA;AAED,MAAM,UAAU,GAAG,CAAC,GAAmB,EAAE,KAAe,EAAE,EAAE;IAC1D,MAAM,CAAC,GAAG,IAAI,kBAAQ,CAAC,GAAG,CAAC,CAAA;IAC3B,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACtB,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AAED,MAAM,WAAW,GAAG,CAAC,GAAe,EAAE,KAAe,EAAE,EAAE;IACvD,MAAM,CAAC,GAAG,IAAI,cAAI,CAAC,GAAG,CAAC,CAAA;IACvB,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACvB,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AAEY,QAAA,MAAM,GAAG,IAAA,6BAAW,EAC/B,cAAc,EACd,UAAU,EACV,UAAU,EACV,WAAW,EACX,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACd,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;QACnB,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAA;IAC7D,CAAC;AACH,CAAC,CACF,CAAA", "sourcesContent": ["import { WriteStream, WriteStreamSync } from '@isaacs/fs-minipass'\nimport { Minipass } from 'minipass'\nimport path from 'node:path'\nimport { list } from './list.js'\nimport { makeCommand } from './make-command.js'\nimport {\n  TarOptions,\n  TarOptionsFile,\n  TarOptionsSync,\n  TarOptionsSyncFile,\n} from './options.js'\nimport { Pack, PackSync } from './pack.js'\n\nconst createFileSync = (opt: TarOptionsSyncFile, files: string[]) => {\n  const p = new PackSync(opt)\n  const stream = new WriteStreamSync(opt.file, {\n    mode: opt.mode || 0o666,\n  })\n  p.pipe(stream as unknown as Minipass.Writable)\n  addFilesSync(p, files)\n}\n\nconst createFile = (opt: TarOptionsFile, files: string[]) => {\n  const p = new Pack(opt)\n  const stream = new WriteStream(opt.file, {\n    mode: opt.mode || 0o666,\n  })\n  p.pipe(stream as unknown as Minipass.Writable)\n\n  const promise = new Promise<void>((res, rej) => {\n    stream.on('error', rej)\n    stream.on('close', res)\n    p.on('error', rej)\n  })\n\n  addFilesAsync(p, files)\n\n  return promise\n}\n\nconst addFilesSync = (p: PackSync, files: string[]) => {\n  files.forEach(file => {\n    if (file.charAt(0) === '@') {\n      list({\n        file: path.resolve(p.cwd, file.slice(1)),\n        sync: true,\n        noResume: true,\n        onReadEntry: entry => p.add(entry),\n      })\n    } else {\n      p.add(file)\n    }\n  })\n  p.end()\n}\n\nconst addFilesAsync = async (\n  p: Pack,\n  files: string[],\n): Promise<void> => {\n  for (let i = 0; i < files.length; i++) {\n    const file = String(files[i])\n    if (file.charAt(0) === '@') {\n      await list({\n        file: path.resolve(String(p.cwd), file.slice(1)),\n        noResume: true,\n        onReadEntry: entry => {\n          p.add(entry)\n        },\n      })\n    } else {\n      p.add(file)\n    }\n  }\n  p.end()\n}\n\nconst createSync = (opt: TarOptionsSync, files: string[]) => {\n  const p = new PackSync(opt)\n  addFilesSync(p, files)\n  return p\n}\n\nconst createAsync = (opt: TarOptions, files: string[]) => {\n  const p = new Pack(opt)\n  addFilesAsync(p, files)\n  return p\n}\n\nexport const create = makeCommand(\n  createFileSync,\n  createFile,\n  createSync,\n  createAsync,\n  (_opt, files) => {\n    if (!files?.length) {\n      throw new TypeError('no paths specified to add to archive')\n    }\n  },\n)\n"]}