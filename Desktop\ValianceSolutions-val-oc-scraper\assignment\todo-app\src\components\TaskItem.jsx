import { useState } from 'react'

function TaskItem({ task, categoryName, onToggle, onDelete, onEdit }) {
  const [isEditing, setIsEditing] = useState(false)
  const [editText, setEditText] = useState(task.text)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const handleEdit = () => {
    if (editText.trim() && editText !== task.text) {
      onEdit(categoryName, task.id, editText.trim())
    }
    setIsEditing(false)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleEdit()
    } else if (e.key === 'Escape') {
      setEditText(task.text)
      setIsEditing(false)
    }
  }

  const handleDelete = () => {
    onDelete(categoryName, task.id)
    setShowDeleteConfirm(false)
  }

  return (
    <>
      <div className={`task-item group animate-slide-in ${task.completed ? 'task-completed' : ''}`}>
        <div className="flex items-center gap-3">
          {/* Checkbox */}
          <button
            onClick={() => onToggle(categoryName, task.id)}
            className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
              task.completed
                ? 'bg-green-500 border-green-500 text-white'
                : 'border-gray-300 hover:border-green-400'
            }`}
          >
            {task.completed && (
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </button>

          {/* Task Text */}
          <div className="flex-1">
            {isEditing ? (
              <input
                type="text"
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                onBlur={handleEdit}
                onKeyDown={handleKeyPress}
                className="w-full bg-transparent border-none outline-none text-sm font-medium"
                autoFocus
              />
            ) : (
              <span
                className={`text-sm font-medium cursor-pointer ${
                  task.completed ? 'line-through opacity-75' : ''
                }`}
                onClick={() => setIsEditing(true)}
              >
                {task.text}
              </span>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {!isEditing && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="p-1 rounded hover:bg-white/50 transition-colors"
                  title="Edit Task"
                >
                  ✏️
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="p-1 rounded hover:bg-red-100 transition-colors text-red-600"
                  title="Delete Task"
                >
                  🗑️
                </button>
              </>
            )}
          </div>
        </div>

        {/* Task completion indicator */}
        {task.completed && (
          <div className="mt-2 text-xs text-green-600 flex items-center gap-1">
            <span>✅</span>
            <span>Completed</span>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-white rounded-xl p-6 max-w-sm mx-4 animate-bounce-in">
            <div className="text-center">
              <div className="text-4xl mb-4">🗑️</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Delete Task?
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete "{task.text}"? This action cannot be undone.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex-1"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default TaskItem
