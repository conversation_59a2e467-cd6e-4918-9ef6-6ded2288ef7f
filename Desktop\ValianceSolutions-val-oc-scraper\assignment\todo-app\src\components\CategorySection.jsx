import { useState } from 'react'
import TaskItem from './TaskItem'
import ProgressBar from './ProgressBar'

function CategorySection({
  categoryName,
  tasks,
  allTasks,
  colorClass,
  onToggleTask,
  onDeleteTask,
  onEditTask,
  onDeleteCategory,
  onAddTask
}) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  // Calculate completion percentage
  const completedTasks = allTasks.filter(task => task.completed).length
  const totalTasks = allTasks.length
  const completionPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0

  // Format category name for display
  const formatCategoryName = (name) => {
    return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  // Get category icon based on name
  const getCategoryIcon = (name) => {
    const icons = {
      personal: '👤',
      work: '💼',
      shopping: '🛒',
      health: '🏥',
      fitness: '💪',
      education: '📚',
      travel: '✈️',
      home: '🏠',
      finance: '💰',
      hobby: '🎨'
    }
    return icons[name.toLowerCase()] || '📋'
  }

  const handleDeleteCategory = () => {
    onDeleteCategory(categoryName)
    setShowDeleteConfirm(false)
  }

  return (
    <div className={`category-card animate-fade-in ${colorClass}`}>
      {/* Category Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <span className="text-2xl">{getCategoryIcon(categoryName)}</span>
          <h3 className="text-lg font-semibold">
            {formatCategoryName(categoryName)}
          </h3>
          <span className="text-sm opacity-75">
            ({tasks.length}/{totalTasks})
          </span>
        </div>

        <div className="flex gap-1">
          <button
            onClick={onAddTask}
            className="p-2 rounded-lg hover:bg-white/50 transition-colors"
            title="Add Task"
          >
            ➕
          </button>
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="p-2 rounded-lg hover:bg-red-100 transition-colors text-red-600"
            title="Delete Category"
          >
            🗑️
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      <ProgressBar
        completed={completedTasks}
        total={totalTasks}
        percentage={completionPercentage}
      />

      {/* Tasks List */}
      <div className="space-y-2 mt-4">
        {tasks.length > 0 ? (
          tasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              categoryName={categoryName}
              onToggle={onToggleTask}
              onDelete={onDeleteTask}
              onEdit={onEditTask}
            />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            <div className="text-3xl mb-2">📝</div>
            <p className="text-sm">No tasks in this category</p>
            <button
              onClick={onAddTask}
              className="mt-2 text-xs bg-white/50 hover:bg-white/70 px-3 py-1 rounded-full transition-colors"
            >
              Add your first task
            </button>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-white rounded-xl p-6 max-w-sm mx-4 animate-bounce-in">
            <div className="text-center">
              <div className="text-4xl mb-4">⚠️</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Delete Category?
              </h3>
              <p className="text-gray-600 mb-6">
                This will permanently delete "{formatCategoryName(categoryName)}" and all its {totalTasks} task(s). This action cannot be undone.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteCategory}
                  className="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex-1"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CategorySection
